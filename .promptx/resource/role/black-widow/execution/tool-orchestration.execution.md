<execution>
  <constraint>
    ## 工具编排核心约束
    - **工具全集限制**: 所有操作基于一个包含150个工具的真实MCP索引。
    - **超级集群架构**: 工具被划分为技术(25)、商业(18)、学术(12)三大超级集群，共覆盖60个核心工具。
    - **高密度并行约束**: 最大支持25个工具同时执行，具备智能负载均衡能力。
    - **动态健康监控**: 对150个工具实现100%的实时健康监控和状态管理。
    - **性能评级要求**: 所有工具具备A/B/C/D四级性能评级，并动态更新。
    - **备选切换机制**: 每个核心工具必须有2-3个备选，主工具失败能在5秒内无缝切换。
  </constraint>

  <rule>
    ## 工具编排强制规则
    - **健康检查前置**: 任何工具调用前，必须通过健康监控检查其可用性。
    - **语义选择强制**: 必须基于任务语义分析、历史模式匹配和工具适配度评分，智能选择最优工具组合。
    - **集群自动选择**: 必须根据任务类型（技术/商业/学术）自动选择并激活对应的超级集群。
    - **并行分组强制**: 集群内工具必须被智能分为3-5个并行组，优化执行效率。
    - **动态降级强制**: 工具连续3次失败后，必须自动降级并切换至性能最佳的备选工具。
    - **效果评估强制**: 每次执行后，必须评估工具效果，并更新其性能评级和历史成功率数据。
  </rule>

  <guideline>
    ## 工具编排指导原则
    - **专业化与互补性**: 每个集群专注特定领域，内部工具功能互补，覆盖完整工作流。
    - **效率最大化**: 通过高密度并行和智能组合，最大化信息获取效率和密度。
    - **稳定性与可靠性**: 通过健康监控和备选机制，确保整体工作流的稳定可靠。
    - **持续学习优化**: 基于使用效果和监控数据，持续优化工具的评级、组合和选择策略。
    - **自动化与透明化**: 工具选择和调度高度自动化，但对用户保持逻辑透明，可解释。
  </guideline>

  <process>
    ## 统一工具编排流程

    ### 阶段1: 工具健康状态初始化与实时监控
    - **启动时**: 加载150个工具的MCP索引，进行批量健康检查，建立初始的A/B/C/D四级性能评级和状态库。
    - **运行时**:
        1.  **预检查**: 工具调用前，访问实时健康状态库。可用则直接调用。
        2.  **实时监控**: 监控调用过程，记录响应时间、成功率。
        3.  **异常处理**: 若失败，触发失败计数。连续3次失败则标记为“不可用”，并立即从备选池中选择最优工具替换。
        4.  **恢复检测**: 对“不可用”工具，启动后台恢复检测任务（每30分钟一次）。

    ### 阶段2: 语义化工具选择
    1.  **任务分析**: 对任务需求进行语义分析，提取领域、意图、复杂度等特征。
    2.  **历史匹配**: 使用 `promptx_recall` 检索相似任务，提取历史成功的工具组合和模式。
    3.  **适配度评估**: 对每个候选工具，综合计算其**语义匹配度(40%)、性能指标(30%)、历史成功率(20%)、用户偏好(10%)**，得出综合适配度评分。
    4.  **组合生成**: 根据任务复杂度，选择最优的工具组合策略（单工具、小组合或超级集群）。

    ### 阶段3: 超级集群激活与高密度执行
    1.  **集群选择**: 根据任务分析结果，激活技术、商业或学术超级集群。
    2.  **智能分组**: 将集群内的20+工具智能划分为3-5个并行组，例如：
        - **组1: 核心搜索矩阵** (并行执行多种搜索工具)
        - **组2: 专业提取矩阵** (并行执行多种内容提取工具)
        - **组3: 深度分析矩阵** (并行执行多种分析和研究工具)
    3.  **高密度执行**: 按批次并行执行所有分组，实时监控各工具状态，动态调整。

    ---
    ## 核心资产：工具超级集群配置 (基于真实MCP索引)

    ### 🔧 技术情报超级集群 (25个核心工具)
    - **GitHub矩阵**: `search_code_github`, `github_search_exa`, `github-api`, etc.
    - **文档与代码库**: `get-library-docs_Context_7`, `codebase-retrieval`, `deepwiki_fetch`, etc.
    - **专业搜索与抓取**: `firecrawl_search`, `firecrawl_deep_research`, `web_search_exa`, etc.

    ### 💼 商业情报超级集群 (18个核心工具)
    - **企业研究**: `company_research_exa`, `linkedin_search_exa`, `competitor_finder_exa`, etc.
    - **全网搜索与提取**: `firecrawl_search`, `tavily_search`, `firecrawl_extract`, `brave_web_search`, etc.
    - **深度分析**: `firecrawl_deep_research`, `sequentialthinking`, etc.

    ### 📚 学术研究超级集群 (12个核心工具)
    - **学术搜索**: `research_paper_search_exa`, `wikipedia_search_exa`, `deep_researcher_start_exa`, etc.
    - **文档处理**: `get-library-docs_Context_7`, `convert_to_markdown`, `firecrawl_extract`, etc.
    - **深度研究与验证**: `firecrawl_deep_research`, `sequentialthinking`, `tavily_search`, etc.

  </process>

  <criteria>
    ## 工具编排质量标准
    - ✅ **功能一致性**: 100%保留了原设计中的工具集群、健康监控、语义选择和高密度执行的所有核心能力。
    - ✅ **工具覆盖率**: 对MCP索引中150个工具的监控覆盖率达到100%。
    - ✅ **调度效率**: 集群选择准确率 > 90%，工具组合优化效果 > 20%。
    - ✅ **系统稳定性**: 备选切换成功率 > 95%，异常自动恢复率 > 95%。
    - ✅ **学习与优化**: 性能评级准确率 > 90%，能基于使用效果持续改进。
  </criteria>
</execution>
